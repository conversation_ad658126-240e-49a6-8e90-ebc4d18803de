'use client';

/**
 * PWA Install Prompt Component
 * Shows a prompt to install the app when available
 */

import React, { useState, useEffect } from 'react';
import { X, Download, Smartphone } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useAppDispatch, useAppSelector } from '@/store';
import { setInstallPromptAvailable } from '@/store/slices/uiSlice';
import { 
  showInstallPrompt, 
  canInstall, 
  isIOS, 
  isAndroid, 
  getInstallInstructions,
  isStandalone 
} from '@/utils/pwa';
import { cn } from '@/utils/cn';

export default function PWAInstallPrompt() {
  const dispatch = useAppDispatch();
  const { installPromptAvailable } = useAppSelector((state) => state.ui);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);
  const [showIOSInstructions, setShowIOSInstructions] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    if (isStandalone()) {
      return;
    }

    // Check if already dismissed this session
    if (typeof window !== 'undefined' && sessionStorage.getItem('pwa-install-dismissed')) {
      setIsDismissed(true);
      return;
    }

    // Listen for PWA install events
    const handleInstallAvailable = () => {
      dispatch(setInstallPromptAvailable(true));
      // Show prompt after a delay to avoid being intrusive
      setTimeout(() => {
        setShowPrompt(true);
      }, 3000);
    };

    const handleInstallCompleted = () => {
      dispatch(setInstallPromptAvailable(false));
      setShowPrompt(false);
    };

    window.addEventListener('pwa-install-available', handleInstallAvailable);
    window.addEventListener('pwa-install-completed', handleInstallCompleted);

    // Check initial state
    if (canInstall()) {
      handleInstallAvailable();
    }

    return () => {
      window.removeEventListener('pwa-install-available', handleInstallAvailable);
      window.removeEventListener('pwa-install-completed', handleInstallCompleted);
    };
  }, [dispatch]);

  const handleInstall = async () => {
    if (isIOS()) {
      setShowIOSInstructions(true);
      return;
    }

    setIsInstalling(true);
    try {
      const installed = await showInstallPrompt();
      if (installed) {
        setShowPrompt(false);
        dispatch(setInstallPromptAvailable(false));
      }
    } catch (error) {
      console.error('Failed to install app:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    setIsDismissed(true);
    // Don't show again for this session
    if (typeof window !== 'undefined') {
      sessionStorage.setItem('pwa-install-dismissed', 'true');
    }
  };

  const handleCloseInstructions = () => {
    setShowIOSInstructions(false);
    setShowPrompt(false);
  };

  // Don't show if already dismissed this session or not available or already installed
  if (isDismissed || !showPrompt || isStandalone()) {
    return null;
  }

  // iOS Instructions Modal
  if (showIOSInstructions) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
        <div className="bg-white rounded-lg max-w-sm w-full p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Install EHS Platform</h3>
            <button
              onClick={handleCloseInstructions}
              className="text-gray-400 hover:text-gray-500"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">1</span>
              </div>
              <p className="text-sm text-gray-700">
                Tap the Share button <span className="inline-block">📤</span> in Safari
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">2</span>
              </div>
              <p className="text-sm text-gray-700">
                Scroll down and tap "Add to Home Screen"
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600 font-semibold text-sm">3</span>
              </div>
              <p className="text-sm text-gray-700">
                Tap "Add" to install the app
              </p>
            </div>
          </div>
          
          <div className="mt-6">
            <Button onClick={handleCloseInstructions} fullWidth>
              Got it!
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Main Install Prompt
  return (
    <div className={cn(
      "fixed bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-40",
      "transform transition-transform duration-300 ease-in-out",
      showPrompt ? "translate-y-0" : "translate-y-full"
    )}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <Smartphone className="h-6 w-6 text-white" />
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className="text-sm font-semibold text-gray-900 mb-1">
            Install EHS Platform
          </h3>
          <p className="text-xs text-gray-600 mb-3">
            Get quick access to safety tools and work offline
          </p>
          
          <div className="flex space-x-2">
            <Button
              size="sm"
              onClick={handleInstall}
              isLoading={isInstalling}
              leftIcon={<Download className="h-4 w-4" />}
            >
              {isIOS() ? 'Instructions' : 'Install'}
            </Button>
            
            <Button
              size="sm"
              variant="ghost"
              onClick={handleDismiss}
            >
              Not now
            </Button>
          </div>
        </div>
        
        <button
          onClick={handleDismiss}
          className="flex-shrink-0 text-gray-400 hover:text-gray-500"
        >
          <X className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}
