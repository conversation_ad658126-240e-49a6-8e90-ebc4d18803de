/**
 * Input Component
 * A flexible input component with various states and styles
 */

import React from 'react';
import { cn } from '@/utils/cn';
import { Eye, EyeOff } from 'lucide-react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  variant?: 'default' | 'filled' | 'outlined';
  inputSize?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type = 'text',
      label,
      error,
      helperText,
      leftIcon,
      rightIcon,
      variant = 'default',
      inputSize = 'md',
      fullWidth = false,
      disabled,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = React.useState(false);
    const [isFocused, setIsFocused] = React.useState(false);

    const isPassword = type === 'password';
    const inputType = isPassword && showPassword ? 'text' : type;

    const baseClasses = [
      'flex items-center',
      'border rounded-lg',
      'transition-all duration-200',
      'focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-opacity-20',
      fullWidth ? 'w-full' : 'w-auto',
    ];

    const variantClasses = {
      default: [
        'bg-white border-gray-300',
        'hover:border-gray-400',
        'focus-within:border-blue-500',
        error ? 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500' : '',
      ],
      filled: [
        'bg-gray-50 border-gray-200',
        'hover:bg-gray-100',
        'focus-within:bg-white focus-within:border-blue-500',
        error ? 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500' : '',
      ],
      outlined: [
        'bg-transparent border-2 border-gray-300',
        'hover:border-gray-400',
        'focus-within:border-blue-500',
        error ? 'border-red-500 focus-within:border-red-500 focus-within:ring-red-500' : '',
      ],
    };

    const sizeClasses = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2.5 text-sm',
      lg: 'px-4 py-3 text-base',
    };

    const inputClasses = [
      'flex-1 bg-transparent border-0 outline-none',
      'placeholder:text-gray-400',
      'disabled:cursor-not-allowed disabled:opacity-50',
    ];

    const containerClasses = cn(
      baseClasses,
      variantClasses[variant],
      sizeClasses[inputSize],
      disabled && 'opacity-50 cursor-not-allowed',
      className
    );

    const togglePasswordVisibility = () => {
      setShowPassword(!showPassword);
    };

    return (
      <div className={fullWidth ? 'w-full' : 'w-auto'}>
        {/* Label */}
        {label && (
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {label}
            {props.required && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        {/* Input Container */}
        <div className={containerClasses}>
          {/* Left Icon */}
          {leftIcon && (
            <div className="flex-shrink-0 mr-3 text-gray-400">
              {leftIcon}
            </div>
          )}

          {/* Input Field */}
          <input
            ref={ref}
            type={inputType}
            className={cn(inputClasses)}
            disabled={disabled}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...props}
          />

          {/* Password Toggle */}
          {isPassword && (
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="flex-shrink-0 ml-3 text-gray-400 hover:text-gray-600 focus:outline-none"
              tabIndex={-1}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </button>
          )}

          {/* Right Icon */}
          {rightIcon && !isPassword && (
            <div className="flex-shrink-0 ml-3 text-gray-400">
              {rightIcon}
            </div>
          )}
        </div>

        {/* Helper Text or Error */}
        {(error || helperText) && (
          <p className={cn(
            'mt-2 text-xs',
            error ? 'text-red-600' : 'text-gray-500'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
