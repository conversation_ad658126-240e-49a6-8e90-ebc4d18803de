'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import {
  Eye,
  Shield,
  FileText,
  AlertTriangle,
  Folder,
  TrendingUp,
  Zap,
  MapPin,
  Bell,
  Search,
  Filter,
  Grid3X3,
  LayoutGrid,
  Home,
  Clock,
  User,
  ChevronRight,
  Calendar,
  Activity,
  Settings,
  Plus,
  MoreHorizontal
} from 'lucide-react';
import { Button, Card, Badge } from '@/components/ui';

export default function STTeleDashboard() {
  const [activeTab, setActiveTab] = useState('My Actions');

  const quickAccessItems = [
    {
      title: 'Observation Reporting',
      icon: Eye,
      gradient: 'bg-gradient-to-br from-blue-500 to-blue-600',
      shadowColor: 'shadow-blue-500/25',
      description: 'Report safety observations',
    },
    {
      title: 'Risk Assessment',
      icon: Shield,
      gradient: 'bg-gradient-to-br from-red-500 to-red-600',
      shadowColor: 'shadow-red-500/25',
      description: 'Assess workplace risks',
    },
    {
      title: 'E-Permit to Work',
      icon: FileText,
      gradient: 'bg-gradient-to-br from-green-500 to-green-600',
      shadowColor: 'shadow-green-500/25',
      description: 'Digital work permits',
    },
    {
      title: 'Incident Reporting',
      icon: AlertTriangle,
      gradient: 'bg-gradient-to-br from-orange-500 to-orange-600',
      shadowColor: 'shadow-orange-500/25',
      description: 'Report incidents',
    },
    {
      title: 'Document Management',
      icon: Folder,
      gradient: 'bg-gradient-to-br from-indigo-500 to-indigo-600',
      shadowColor: 'shadow-indigo-500/25',
      description: 'Manage documents',
    },
    {
      title: 'Statistic Report',
      icon: TrendingUp,
      gradient: 'bg-gradient-to-br from-purple-500 to-purple-600',
      shadowColor: 'shadow-purple-500/25',
      description: 'View analytics',
    },
    {
      title: 'Audit',
      icon: Zap,
      gradient: 'bg-gradient-to-br from-pink-500 to-pink-600',
      shadowColor: 'shadow-pink-500/25',
      description: 'Conduct audits',
    },
    {
      title: 'AZ Track',
      icon: MapPin,
      gradient: 'bg-gradient-to-br from-cyan-500 to-cyan-600',
      shadowColor: 'shadow-cyan-500/25',
      description: 'Location tracking',
    },
  ];

  const myActions = [
    {
      id: 1,
      title: 'Complete Risk Assessment',
      subtitle: 'RA-2024-045 for electrical work',
      status: 'Pending',
      statusColor: 'bg-orange-100 text-orange-800',
      dueDate: 'Due Today',
      borderColor: 'border-l-red-500',
    },
    {
      id: 2,
      title: 'Review Safety Observation',
      subtitle: 'SO-2024-123 - PPE compliance',
      status: 'In Progress',
      statusColor: 'bg-blue-100 text-blue-800',
      dueDate: 'Due Tomorrow',
      borderColor: 'border-l-orange-500',
    },
  ];

  const draftItems = [
    {
      id: 1,
      title: 'Draft Risk Assessment',
      subtitle: 'Electrical work assessment',
      count: 4,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 ios-safe-area">
      {/* Header Card Section */}
      <div className="p-4">
        <Card className="p-4 border-0 shadow-sm">
          {/* Company Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <Image
                src="/assests/no-image.jpg"
                alt="STTelemedia Logo"
                width={180}
                height={120}
                className="object-contain"
              />
            </div>
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Bell className="h-6 w-6 text-gray-600" />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                  <span className="text-xs text-white font-medium">4</span>
                </div>
              </div>
            </div>
          </div>

          {/* Welcome Section */}
          <div className="pt-4">
            <h2 className="text-xl font-medium text-gray-900">Hello, JC Sekar</h2>
          </div>
        </Card>
      </div>

      {/* Quick Access */}
      <div className="px-4 py-2 quick-access-mobile">
        <h3 className="text-lg md:text-lg text-base font-semibold text-gray-900 mb-3 md:mb-4 px-1">Quick Access</h3>
        <div className="grid grid-cols-4 gap-3 sm:gap-4 quick-access-grid">
          {quickAccessItems.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <Card
                key={index}
                className={`group p-4 md:p-4 quick-access-card text-center hover:shadow-xl hover:-translate-y-1 transition-all duration-300 cursor-pointer border-0 shadow-sm bg-white touch-feedback relative overflow-hidden animate-fade-in-up stagger-${index + 1}`}
                clickable
                hover
              >
                {/* Background gradient overlay on hover */}
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <div className="relative z-10">
                  <div className={`${item.gradient} w-14 h-14 md:w-14 md:h-14 quick-access-icon rounded-2xl md:rounded-2xl rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-3 shadow-lg ${item.shadowColor} group-hover:scale-110 transition-transform duration-300`}>
                    <IconComponent className="h-7 w-7 md:h-7 md:w-7 text-white" />
                  </div>
                  <p className="text-xs md:text-xs quick-access-title text-gray-800 font-semibold leading-tight line-clamp-2">{item.title}</p>
                </div>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Tabs */}
      <div className="px-4 py-4">
        <div className="flex space-x-8 border-b border-gray-200">
          <button
            onClick={() => setActiveTab('My Actions')}
            className={`pb-3 px-1 text-sm font-semibold transition-all duration-200 relative ${
              activeTab === 'My Actions'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            My Actions
            <Badge
              variant="error"
              size="sm"
              rounded
              className="ml-2 min-w-[20px] h-5 text-xs font-semibold"
            >
              5
            </Badge>
          </button>
          <button
            onClick={() => setActiveTab('Draft')}
            className={`pb-3 px-1 text-sm font-semibold transition-all duration-200 relative ${
              activeTab === 'Draft'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Draft
            <Badge
              variant="error"
              size="sm"
              rounded
              className="ml-2 min-w-[20px] h-5 text-xs font-semibold"
            >
              4
            </Badge>
          </button>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="px-4 py-3 flex items-center space-x-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search actions..."
            className="w-full pl-10 pr-4 py-3 bg-white border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
          />
        </div>
        <button className="p-3 bg-white border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors duration-200 shadow-sm touch-feedback">
          <Filter className="h-4 w-4 text-gray-600" />
        </button>
      </div>

      {/* Content */}
      <div className="px-4 pb-24">
        {activeTab === 'My Actions' && (
          <div className="space-y-4">
            {myActions.map((action, index) => (
              <div
                key={action.id}
                className={`clean-action-card ${action.borderColor} group cursor-pointer`}
              >
                {/* Header section with title and status */}
                <div className="flex items-start justify-between">
                  <h4 className="card-title flex-1 pr-4">{action.title}</h4>
                  <Badge className={`${action.statusColor} shadow-sm flex-shrink-0`} size="sm" rounded>
                    {action.status}
                  </Badge>
                </div>

                {/* Subtitle */}
                <p className="card-subtitle">{action.subtitle}</p>

                {/* Bottom section with due date and action */}
                <div className="card-footer">
                  <p className="card-due-date">{action.dueDate}</p>
                  <button className="card-action-btn group/btn touch-feedback">
                    <span>View Details</span>
                    <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-0.5 transition-transform duration-200" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeTab === 'Draft' && (
          <div className="space-y-4">
            {draftItems.map((item) => (
              <Card
                key={item.id}
                className="group p-6 hover:shadow-xl hover:-translate-y-0.5 transition-all duration-300 bg-gradient-to-r from-white to-gray-50/50 border-l-4 border-l-amber-400 relative overflow-hidden"
                hover
              >
                {/* Draft indicator pattern */}
                <div className="absolute top-0 right-0 w-20 h-20 opacity-5">
                  <div className="w-full h-full bg-amber-400 transform rotate-45 translate-x-10 -translate-y-10"></div>
                </div>

                <div className="relative z-10">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-3">
                        <h4 className="font-bold text-gray-900 text-base">{item.title}</h4>
                        <Badge variant="warning" size="sm" className="bg-amber-100 text-amber-800 border-amber-200">
                          Draft
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-4 leading-relaxed">{item.subtitle}</p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <Badge variant="secondary" size="sm" rounded className="bg-gray-100 text-gray-700">
                            {item.count} items
                          </Badge>
                          <div className="flex items-center space-x-1.5 text-xs text-gray-500">
                            <Clock className="h-3.5 w-3.5" />
                            <span>Last edited 2 hours ago</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button className="p-2 rounded-lg bg-gray-100 hover:bg-gray-200 transition-colors touch-feedback">
                            <Settings className="h-4 w-4 text-gray-600" />
                          </button>
                          <button className="group/btn flex items-center space-x-1 text-blue-600 text-sm font-semibold hover:text-blue-700 transition-all duration-200 touch-feedback">
                            <span>Continue</span>
                            <ChevronRight className="h-4 w-4 group-hover/btn:translate-x-0.5 transition-transform duration-200" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            {/* Add new draft button */}
            <Card className="group p-6 border-2 border-dashed border-gray-300 hover:border-blue-400 hover:bg-blue-50/30 transition-all duration-300 cursor-pointer touch-feedback">
              <div className="flex items-center justify-center space-x-3 text-gray-500 group-hover:text-blue-600">
                <Plus className="h-5 w-5" />
                <span className="font-medium">Create New Draft</span>
              </div>
            </Card>
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-lg border-t border-gray-200/50 px-4 py-2 shadow-2xl ios-safe-area">
        {/* Navigation background with subtle gradient */}
        <div className="absolute inset-0 bg-gradient-to-t from-white via-white to-gray-50/30"></div>

        <div className="relative z-10 flex justify-around items-center max-w-md mx-auto">
          <button className="group flex flex-col items-center py-3 px-3 text-gray-500 hover:text-blue-600 transition-all duration-200 touch-feedback min-w-[64px] rounded-xl hover:bg-blue-50">
            <LayoutGrid className="h-5 w-5 mb-1 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-xs font-medium">Dashboard</span>
          </button>

          <button className="group flex flex-col items-center py-3 px-3 text-gray-500 hover:text-blue-600 transition-all duration-200 touch-feedback min-w-[64px] rounded-xl hover:bg-blue-50">
            <Grid3X3 className="h-5 w-5 mb-1 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-xs font-medium">Modules</span>
          </button>

          {/* Active Home button with enhanced styling */}
          <button className="group flex flex-col items-center py-3 px-3 text-blue-600 relative touch-feedback min-w-[64px] rounded-xl bg-blue-50 shadow-sm">
            <div className="absolute inset-0 bg-gradient-to-t from-blue-100/50 to-blue-50/50 rounded-xl"></div>
            <Home className="relative h-5 w-5 mb-1 scale-110" />
            <span className="relative text-xs font-bold">Home</span>
            <div className="absolute -bottom-1 w-10 h-1 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full shadow-sm"></div>
          </button>

          <button className="group flex flex-col items-center py-3 px-3 text-gray-500 hover:text-blue-600 transition-all duration-200 touch-feedback min-w-[64px] rounded-xl hover:bg-blue-50">
            <Clock className="h-5 w-5 mb-1 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-xs font-medium">History</span>
          </button>

          <button className="group flex flex-col items-center py-3 px-3 text-gray-500 hover:text-blue-600 transition-all duration-200 touch-feedback min-w-[64px] rounded-xl hover:bg-blue-50 relative">
            <User className="h-5 w-5 mb-1 group-hover:scale-110 transition-transform duration-200" />
            <span className="text-xs font-medium">Profile</span>
            {/* Profile notification dot */}
            <div className="absolute top-2 right-2 w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
          </button>
        </div>
      </div>
    </div>
  );
}
