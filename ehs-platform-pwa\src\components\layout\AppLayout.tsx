'use client';

/**
 * Main App Layout Component
 */

import React, { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { setOnlineStatus, setStandaloneMode } from '@/store/slices/uiSlice';
import { initializePWA, isStandalone, registerNetworkListeners } from '@/utils/pwa';
import { cn } from '@/utils/cn';
import PWAInstallPrompt from '@/components/features/PWAInstallPrompt';
import OfflineIndicator from '@/components/features/OfflineIndicator';

interface AppLayoutProps {
  children: React.ReactNode;
}

export default function AppLayout({ children }: AppLayoutProps) {
  const dispatch = useAppDispatch();
  const { isOnline, sidebarOpen } = useAppSelector((state) => state.ui);

  useEffect(() => {
    // Initialize PWA functionality
    initializePWA();
    
    // Set initial standalone mode
    dispatch(setStandaloneMode(isStandalone()));
    
    // Register network status listeners
    const cleanup = registerNetworkListeners(
      () => dispatch(setOnlineStatus(true)),
      () => dispatch(setOnlineStatus(false))
    );

    return cleanup;
  }, [dispatch]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Offline/Online Status Indicator */}
      <OfflineIndicator />

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* PWA Install Prompt */}
      <PWAInstallPrompt />
    </div>
  );
}
