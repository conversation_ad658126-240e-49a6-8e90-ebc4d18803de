/**
 * Badge Component
 * A small status indicator or label component
 */

import React from 'react';
import { cn } from '@/utils/cn';

interface BadgeProps extends React.HTMLAttributes<HTMLSpanElement> {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  dot?: boolean;
}

const Badge = React.forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'default', size = 'md', rounded = false, dot = false, children, ...props }, ref) => {
    const baseClasses = [
      'inline-flex items-center justify-center',
      'font-medium',
      'transition-colors duration-200',
      rounded ? 'rounded-full' : 'rounded-md',
    ];

    const variantClasses = {
      default: 'bg-gray-100 text-gray-800 border border-gray-200',
      success: 'bg-green-100 text-green-800 border border-green-200',
      warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
      error: 'bg-red-100 text-red-800 border border-red-200',
      info: 'bg-blue-100 text-blue-800 border border-blue-200',
      secondary: 'bg-purple-100 text-purple-800 border border-purple-200',
    };

    const sizeClasses = {
      sm: dot ? 'w-2 h-2' : 'px-2 py-0.5 text-xs',
      md: dot ? 'w-2.5 h-2.5' : 'px-2.5 py-1 text-xs',
      lg: dot ? 'w-3 h-3' : 'px-3 py-1.5 text-sm',
    };

    if (dot) {
      return (
        <span
          ref={ref}
          className={cn(
            'rounded-full',
            variantClasses[variant].split(' ')[0], // Just the background color
            sizeClasses[size],
            className
          )}
          {...props}
        />
      );
    }

    return (
      <span
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          className
        )}
        {...props}
      >
        {children}
      </span>
    );
  }
);

Badge.displayName = 'Badge';

export default Badge;
