/**
 * PWA Utilities for EHS Platform
 * Handles installation prompts, offline detection, and service worker management
 */

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

declare global {
  interface WindowEventMap {
    beforeinstallprompt: BeforeInstallPromptEvent;
  }
}

let deferredPrompt: BeforeInstallPromptEvent | null = null;

/**
 * Initialize PWA installation prompt handling
 */
export function initializePWA() {
  if (typeof window === 'undefined') return;

  // Listen for the beforeinstallprompt event
  window.addEventListener('beforeinstallprompt', (e) => {
    // Prevent the mini-infobar from appearing on mobile
    e.preventDefault();
    // Stash the event so it can be triggered later
    deferredPrompt = e;
    
    // Update UI to notify the user they can install the PWA
    showInstallPromotion();
  });

  // Listen for the app being installed
  window.addEventListener('appinstalled', () => {
    console.log('PWA was installed');
    hideInstallPromotion();
    deferredPrompt = null;
  });
}

/**
 * Show the install app prompt
 */
export async function showInstallPrompt(): Promise<boolean> {
  if (!deferredPrompt) {
    return false;
  }

  // Show the install prompt
  deferredPrompt.prompt();
  
  // Wait for the user to respond to the prompt
  const { outcome } = await deferredPrompt.userChoice;
  
  // We've used the prompt, and can't use it again, throw it away
  deferredPrompt = null;
  
  return outcome === 'accepted';
}

/**
 * Check if the app can be installed
 */
export function canInstall(): boolean {
  return deferredPrompt !== null;
}

/**
 * Check if the app is running in standalone mode (installed)
 */
export function isStandalone(): boolean {
  if (typeof window === 'undefined') return false;
  
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true ||
    document.referrer.includes('android-app://')
  );
}

/**
 * Check if the device is online
 */
export function isOnline(): boolean {
  if (typeof navigator === 'undefined') return true;
  return navigator.onLine;
}

/**
 * Register online/offline event listeners
 */
export function registerNetworkListeners(
  onOnline: () => void,
  onOffline: () => void
) {
  if (typeof window === 'undefined') return;

  window.addEventListener('online', onOnline);
  window.addEventListener('offline', onOffline);

  return () => {
    window.removeEventListener('online', onOnline);
    window.removeEventListener('offline', onOffline);
  };
}

/**
 * Get device information for analytics
 */
export function getDeviceInfo() {
  if (typeof navigator === 'undefined') return null;

  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    standalone: isStandalone(),
  };
}

/**
 * Check if the browser supports PWA features
 */
export function supportsPWA(): boolean {
  if (typeof window === 'undefined') return false;

  return (
    'serviceWorker' in navigator &&
    'PushManager' in window &&
    'Notification' in window
  );
}

/**
 * Request notification permission
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    return 'denied';
  }

  if (Notification.permission === 'granted') {
    return 'granted';
  }

  if (Notification.permission !== 'denied') {
    const permission = await Notification.requestPermission();
    return permission;
  }

  return Notification.permission;
}

/**
 * Show a notification
 */
export function showNotification(
  title: string,
  options?: NotificationOptions
): Notification | null {
  if (!('Notification' in window) || Notification.permission !== 'granted') {
    return null;
  }

  return new Notification(title, {
    icon: '/icons/icon-192x192.png',
    badge: '/icons/icon-72x72.png',
    ...options,
  });
}

// Internal functions for UI updates
function showInstallPromotion() {
  // Dispatch custom event for components to listen to
  window.dispatchEvent(new CustomEvent('pwa-install-available'));
}

function hideInstallPromotion() {
  // Dispatch custom event for components to listen to
  window.dispatchEvent(new CustomEvent('pwa-install-completed'));
}

/**
 * Check if running on iOS
 */
export function isIOS(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return /iPad|iPhone|iPod/.test(navigator.userAgent);
}

/**
 * Check if running on Android
 */
export function isAndroid(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  return /Android/.test(navigator.userAgent);
}

/**
 * Get install instructions based on device
 */
export function getInstallInstructions(): string {
  if (isIOS()) {
    return 'Tap the Share button and then "Add to Home Screen"';
  }
  
  if (isAndroid()) {
    return 'Tap the menu button and then "Add to Home Screen" or "Install App"';
  }
  
  return 'Look for the install button in your browser\'s address bar';
}
