{"name": "ehs-platform-pwa", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@reduxjs/toolkit": "^2.8.2", "@types/workbox-sw": "^4.3.7", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "15.4.2", "next-pwa": "^5.6.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.60.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.3.1", "workbox-webpack-plugin": "^7.3.0", "zod": "^4.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.6.2", "tailwindcss": "^4", "typescript": "^5"}}