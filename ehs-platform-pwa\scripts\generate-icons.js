/**
 * Generate PWA icons script
 * This script creates placeholder icons for the PWA
 */

const fs = require('fs');
const path = require('path');

const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
const iconsDir = path.join(__dirname, '..', 'public', 'icons');

// Ensure icons directory exists
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Generate SVG icon template
function generateSVGIcon(size) {
  return `<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <rect width="${size}" height="${size}" fill="#1e40af" rx="${size * 0.1}"/>
  <g transform="translate(${size * 0.2}, ${size * 0.2})">
    <path d="M${size * 0.3} ${size * 0.1}L${size * 0.5} ${size * 0.3}L${size * 0.3} ${size * 0.5}L${size * 0.1} ${size * 0.3}Z" fill="white"/>
    <rect x="${size * 0.1}" y="${size * 0.35}" width="${size * 0.4}" height="${size * 0.05}" fill="white"/>
    <rect x="${size * 0.1}" y="${size * 0.45}" width="${size * 0.4}" height="${size * 0.05}" fill="white"/>
  </g>
  <text x="${size * 0.5}" y="${size * 0.85}" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="${size * 0.08}" font-weight="bold">EHS</text>
</svg>`;
}

// Generate icons for each size
sizes.forEach(size => {
  const svgContent = generateSVGIcon(size);
  const filename = `icon-${size}x${size}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated ${filename}`);
});

// Generate shortcut icons
const shortcuts = [
  { name: 'report', color: '#dc2626', icon: '⚠' },
  { name: 'checklist', color: '#16a34a', icon: '✓' },
  { name: 'risk', color: '#2563eb', icon: '🛡' }
];

shortcuts.forEach(shortcut => {
  const svgContent = `<svg width="96" height="96" viewBox="0 0 96 96" xmlns="http://www.w3.org/2000/svg">
  <rect width="96" height="96" fill="${shortcut.color}" rx="9.6"/>
  <text x="48" y="60" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="32">${shortcut.icon}</text>
</svg>`;
  
  const filename = `shortcut-${shortcut.name}.svg`;
  const filepath = path.join(iconsDir, filename);
  
  fs.writeFileSync(filepath, svgContent);
  console.log(`Generated ${filename}`);
});

console.log('All icons generated successfully!');
