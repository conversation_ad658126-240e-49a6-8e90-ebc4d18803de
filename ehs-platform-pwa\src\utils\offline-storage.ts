/**
 * Offline Storage Utilities for EHS Platform
 * Handles local data storage, synchronization, and offline capabilities
 */

import { openDB, DBSchema, IDBPDatabase } from 'idb';

// Database schema definition
interface EHSDatabase extends DBSchema {
  incidents: {
    key: string;
    value: {
      id: string;
      title: string;
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      location: string;
      coordinates?: { lat: number; lng: number };
      reportedBy: string;
      reportedAt: string;
      status: 'draft' | 'pending' | 'submitted' | 'synced';
      photos?: string[];
      witnesses?: string[];
      immediateActions?: string;
      createdAt: string;
      updatedAt: string;
      syncedAt?: string;
    };
    indexes: { 'by-status': string; 'by-date': string };
  };
  checklists: {
    key: string;
    value: {
      id: string;
      templateId: string;
      title: string;
      items: Array<{
        id: string;
        text: string;
        checked: boolean;
        notes?: string;
        photos?: string[];
      }>;
      completedBy: string;
      completedAt?: string;
      location: string;
      status: 'draft' | 'completed' | 'synced';
      createdAt: string;
      updatedAt: string;
      syncedAt?: string;
    };
    indexes: { 'by-status': string; 'by-template': string };
  };
  riskAssessments: {
    key: string;
    value: {
      id: string;
      title: string;
      description: string;
      hazards: Array<{
        id: string;
        description: string;
        likelihood: number;
        severity: number;
        riskLevel: 'low' | 'medium' | 'high' | 'critical';
        controls: string[];
      }>;
      assessedBy: string;
      assessedAt: string;
      location: string;
      status: 'draft' | 'completed' | 'synced';
      createdAt: string;
      updatedAt: string;
      syncedAt?: string;
    };
    indexes: { 'by-status': string; 'by-date': string };
  };
  syncQueue: {
    key: string;
    value: {
      id: string;
      type: 'incident' | 'checklist' | 'riskAssessment';
      action: 'create' | 'update' | 'delete';
      data: any;
      retryCount: number;
      createdAt: string;
      lastAttempt?: string;
    };
    indexes: { 'by-type': string; 'by-retry': number };
  };
}

let db: IDBPDatabase<EHSDatabase> | null = null;

/**
 * Initialize the offline database
 */
export async function initializeOfflineDB(): Promise<IDBPDatabase<EHSDatabase>> {
  if (db) return db;

  db = await openDB<EHSDatabase>('ehs-platform', 1, {
    upgrade(db) {
      // Incidents store
      const incidentsStore = db.createObjectStore('incidents', {
        keyPath: 'id',
      });
      incidentsStore.createIndex('by-status', 'status');
      incidentsStore.createIndex('by-date', 'createdAt');

      // Checklists store
      const checklistsStore = db.createObjectStore('checklists', {
        keyPath: 'id',
      });
      checklistsStore.createIndex('by-status', 'status');
      checklistsStore.createIndex('by-template', 'templateId');

      // Risk assessments store
      const riskAssessmentsStore = db.createObjectStore('riskAssessments', {
        keyPath: 'id',
      });
      riskAssessmentsStore.createIndex('by-status', 'status');
      riskAssessmentsStore.createIndex('by-date', 'createdAt');

      // Sync queue store
      const syncQueueStore = db.createObjectStore('syncQueue', {
        keyPath: 'id',
      });
      syncQueueStore.createIndex('by-type', 'type');
      syncQueueStore.createIndex('by-retry', 'retryCount');
    },
  });

  return db;
}

/**
 * Save incident data offline
 */
export async function saveIncidentOffline(incident: EHSDatabase['incidents']['value']) {
  const database = await initializeOfflineDB();
  await database.put('incidents', incident);
  
  // Add to sync queue if not already synced
  if (incident.status !== 'synced') {
    await addToSyncQueue('incident', 'create', incident);
  }
}

/**
 * Get all incidents from offline storage
 */
export async function getOfflineIncidents(status?: string) {
  const database = await initializeOfflineDB();
  
  if (status) {
    return await database.getAllFromIndex('incidents', 'by-status', status);
  }
  
  return await database.getAll('incidents');
}

/**
 * Save checklist data offline
 */
export async function saveChecklistOffline(checklist: EHSDatabase['checklists']['value']) {
  const database = await initializeOfflineDB();
  await database.put('checklists', checklist);
  
  // Add to sync queue if not already synced
  if (checklist.status !== 'synced') {
    await addToSyncQueue('checklist', 'create', checklist);
  }
}

/**
 * Get all checklists from offline storage
 */
export async function getOfflineChecklists(status?: string) {
  const database = await initializeOfflineDB();
  
  if (status) {
    return await database.getAllFromIndex('checklists', 'by-status', status);
  }
  
  return await database.getAll('checklists');
}

/**
 * Save risk assessment data offline
 */
export async function saveRiskAssessmentOffline(assessment: EHSDatabase['riskAssessments']['value']) {
  const database = await initializeOfflineDB();
  await database.put('riskAssessments', assessment);
  
  // Add to sync queue if not already synced
  if (assessment.status !== 'synced') {
    await addToSyncQueue('riskAssessment', 'create', assessment);
  }
}

/**
 * Get all risk assessments from offline storage
 */
export async function getOfflineRiskAssessments(status?: string) {
  const database = await initializeOfflineDB();
  
  if (status) {
    return await database.getAllFromIndex('riskAssessments', 'by-status', status);
  }
  
  return await database.getAll('riskAssessments');
}

/**
 * Add item to sync queue
 */
export async function addToSyncQueue(
  type: 'incident' | 'checklist' | 'riskAssessment',
  action: 'create' | 'update' | 'delete',
  data: any
) {
  const database = await initializeOfflineDB();
  const queueItem: EHSDatabase['syncQueue']['value'] = {
    id: `${type}-${action}-${data.id}-${Date.now()}`,
    type,
    action,
    data,
    retryCount: 0,
    createdAt: new Date().toISOString(),
  };
  
  await database.put('syncQueue', queueItem);
}

/**
 * Get pending sync items
 */
export async function getPendingSyncItems() {
  const database = await initializeOfflineDB();
  return await database.getAll('syncQueue');
}

/**
 * Remove item from sync queue
 */
export async function removeFromSyncQueue(id: string) {
  const database = await initializeOfflineDB();
  await database.delete('syncQueue', id);
}

/**
 * Update sync item retry count
 */
export async function updateSyncItemRetry(id: string) {
  const database = await initializeOfflineDB();
  const item = await database.get('syncQueue', id);
  
  if (item) {
    item.retryCount += 1;
    item.lastAttempt = new Date().toISOString();
    await database.put('syncQueue', item);
  }
}

/**
 * Clear all offline data (for logout/reset)
 */
export async function clearOfflineData() {
  const database = await initializeOfflineDB();
  
  await database.clear('incidents');
  await database.clear('checklists');
  await database.clear('riskAssessments');
  await database.clear('syncQueue');
}

/**
 * Get storage usage statistics
 */
export async function getStorageStats() {
  const database = await initializeOfflineDB();
  
  const [incidents, checklists, riskAssessments, syncQueue] = await Promise.all([
    database.count('incidents'),
    database.count('checklists'),
    database.count('riskAssessments'),
    database.count('syncQueue'),
  ]);
  
  return {
    incidents,
    checklists,
    riskAssessments,
    syncQueue,
    total: incidents + checklists + riskAssessments + syncQueue,
  };
}
