/**
 * UI State Redux Slice
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

interface Modal {
  id: string;
  type: string;
  props?: Record<string, any>;
  onClose?: () => void;
}

interface UIState {
  // Theme and appearance
  theme: 'light' | 'dark' | 'system';
  sidebarOpen: boolean;
  
  // Loading states
  globalLoading: boolean;
  loadingMessage?: string;
  
  // Toasts and notifications
  toasts: Toast[];
  
  // Modals and dialogs
  modals: Modal[];
  
  // PWA state
  installPromptAvailable: boolean;
  isOnline: boolean;
  isStandalone: boolean;
  
  // Navigation
  currentPage: string;
  previousPage?: string;
  
  // Form states
  unsavedChanges: boolean;
  
  // Search and filters
  searchQuery: string;
  activeFilters: Record<string, any>;
  
  // Layout preferences
  viewMode: 'grid' | 'list';
  itemsPerPage: number;
}

const initialState: UIState = {
  theme: 'system',
  sidebarOpen: false,
  globalLoading: false,
  toasts: [],
  modals: [],
  installPromptAvailable: false,
  isOnline: true,
  isStandalone: false,
  currentPage: '/',
  unsavedChanges: false,
  searchQuery: '',
  activeFilters: {},
  viewMode: 'list',
  itemsPerPage: 20,
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Theme actions
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },
    
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    // Loading actions
    setGlobalLoading: (state, action: PayloadAction<{ loading: boolean; message?: string }>) => {
      state.globalLoading = action.payload.loading;
      state.loadingMessage = action.payload.message;
    },
    
    // Toast actions
    addToast: (state, action: PayloadAction<Omit<Toast, 'id'>>) => {
      const toast: Toast = {
        id: Date.now().toString(),
        duration: 5000,
        ...action.payload,
      };
      state.toasts.push(toast);
    },
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    clearToasts: (state) => {
      state.toasts = [];
    },
    
    // Modal actions
    openModal: (state, action: PayloadAction<Omit<Modal, 'id'>>) => {
      const modal: Modal = {
        id: Date.now().toString(),
        ...action.payload,
      };
      state.modals.push(modal);
    },
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals = state.modals.filter(modal => modal.id !== action.payload);
    },
    closeAllModals: (state) => {
      state.modals = [];
    },
    
    // PWA actions
    setInstallPromptAvailable: (state, action: PayloadAction<boolean>) => {
      state.installPromptAvailable = action.payload;
    },
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    setStandaloneMode: (state, action: PayloadAction<boolean>) => {
      state.isStandalone = action.payload;
    },
    
    // Navigation actions
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.previousPage = state.currentPage;
      state.currentPage = action.payload;
    },
    
    // Form actions
    setUnsavedChanges: (state, action: PayloadAction<boolean>) => {
      state.unsavedChanges = action.payload;
    },
    
    // Search and filter actions
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setActiveFilters: (state, action: PayloadAction<Record<string, any>>) => {
      state.activeFilters = action.payload;
    },
    updateFilter: (state, action: PayloadAction<{ key: string; value: any }>) => {
      state.activeFilters[action.payload.key] = action.payload.value;
    },
    removeFilter: (state, action: PayloadAction<string>) => {
      delete state.activeFilters[action.payload];
    },
    clearFilters: (state) => {
      state.activeFilters = {};
      state.searchQuery = '';
    },
    
    // Layout actions
    setViewMode: (state, action: PayloadAction<'grid' | 'list'>) => {
      state.viewMode = action.payload;
    },
    setItemsPerPage: (state, action: PayloadAction<number>) => {
      state.itemsPerPage = action.payload;
    },
  },
});

export const {
  setTheme,
  toggleSidebar,
  setSidebarOpen,
  setGlobalLoading,
  addToast,
  removeToast,
  clearToasts,
  openModal,
  closeModal,
  closeAllModals,
  setInstallPromptAvailable,
  setOnlineStatus,
  setStandaloneMode,
  setCurrentPage,
  setUnsavedChanges,
  setSearchQuery,
  setActiveFilters,
  updateFilter,
  removeFilter,
  clearFilters,
  setViewMode,
  setItemsPerPage,
} = uiSlice.actions;

export default uiSlice.reducer;

// Selectors
export const selectUI = (state: { ui: UIState }) => state.ui;
export const selectTheme = (state: { ui: UIState }) => state.ui.theme;
export const selectSidebarOpen = (state: { ui: UIState }) => state.ui.sidebarOpen;
export const selectGlobalLoading = (state: { ui: UIState }) => state.ui.globalLoading;
export const selectToasts = (state: { ui: UIState }) => state.ui.toasts;
export const selectModals = (state: { ui: UIState }) => state.ui.modals;
export const selectInstallPromptAvailable = (state: { ui: UIState }) => state.ui.installPromptAvailable;
export const selectIsOnline = (state: { ui: UIState }) => state.ui.isOnline;
export const selectIsStandalone = (state: { ui: UIState }) => state.ui.isStandalone;
export const selectCurrentPage = (state: { ui: UIState }) => state.ui.currentPage;
export const selectUnsavedChanges = (state: { ui: UIState }) => state.ui.unsavedChanges;
export const selectSearchQuery = (state: { ui: UIState }) => state.ui.searchQuery;
export const selectActiveFilters = (state: { ui: UIState }) => state.ui.activeFilters;
export const selectViewMode = (state: { ui: UIState }) => state.ui.viewMode;
export const selectItemsPerPage = (state: { ui: UIState }) => state.ui.itemsPerPage;
