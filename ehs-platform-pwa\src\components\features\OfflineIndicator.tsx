'use client';

/**
 * Offline Indicator Component
 * Shows when the app is offline and sync status
 */

import React, { useState, useEffect } from 'react';
import { WifiOff, Wifi, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/store';
import { setOnlineStatus } from '@/store/slices/uiSlice';
import { syncOfflineData, selectPendingUploads, selectIsSyncing, selectSyncError } from '@/store/slices/offlineSlice';
import { cn } from '@/utils/cn';
import Button from '@/components/ui/Button';

export default function OfflineIndicator() {
  const dispatch = useAppDispatch();
  const { isOnline } = useAppSelector((state) => state.ui);
  const pendingUploads = useAppSelector(selectPendingUploads);
  const isSyncing = useAppSelector(selectIsSyncing);
  const syncError = useAppSelector(selectSyncError);
  
  const [showDetails, setShowDetails] = useState(false);
  const [justCameOnline, setJustCameOnline] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      dispatch(setOnlineStatus(true));
      setJustCameOnline(true);
      
      // Auto-sync when coming back online
      if (pendingUploads > 0) {
        dispatch(syncOfflineData());
      }
      
      // Hide the "just came online" indicator after 3 seconds
      setTimeout(() => setJustCameOnline(false), 3000);
    };

    const handleOffline = () => {
      dispatch(setOnlineStatus(false));
      setJustCameOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial state
    dispatch(setOnlineStatus(navigator.onLine));

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [dispatch, pendingUploads]);

  const handleManualSync = () => {
    if (isOnline && pendingUploads > 0) {
      dispatch(syncOfflineData());
    }
  };

  const handleToggleDetails = () => {
    setShowDetails(!showDetails);
  };

  // Don't show anything if online and no pending uploads
  if (isOnline && pendingUploads === 0 && !justCameOnline && !syncError) {
    return null;
  }

  return (
    <div className="fixed top-0 left-0 right-0 z-50">
      {/* Main Status Bar */}
      <div
        className={cn(
          "px-4 py-2 text-white text-sm font-medium transition-colors duration-300",
          !isOnline ? "bg-red-500" : 
          justCameOnline ? "bg-green-500" :
          syncError ? "bg-orange-500" :
          pendingUploads > 0 ? "bg-yellow-500" : "bg-blue-500"
        )}
      >
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          <div className="flex items-center space-x-2">
            {!isOnline ? (
              <>
                <WifiOff className="h-4 w-4" />
                <span>You're offline. Data will be saved locally.</span>
              </>
            ) : justCameOnline ? (
              <>
                <Wifi className="h-4 w-4" />
                <span>Back online!</span>
              </>
            ) : syncError ? (
              <>
                <AlertCircle className="h-4 w-4" />
                <span>Sync failed. Tap to retry.</span>
              </>
            ) : isSyncing ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Syncing {pendingUploads} items...</span>
              </>
            ) : pendingUploads > 0 ? (
              <>
                <RefreshCw className="h-4 w-4" />
                <span>{pendingUploads} items waiting to sync</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                <span>All data synced</span>
              </>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {isOnline && (pendingUploads > 0 || syncError) && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleManualSync}
                disabled={isSyncing}
                className="text-white hover:bg-white hover:bg-opacity-20 border-white border-opacity-30"
              >
                {isSyncing ? (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                ) : (
                  <RefreshCw className="h-3 w-3" />
                )}
                <span className="ml-1">Sync</span>
              </Button>
            )}
            
            {(pendingUploads > 0 || syncError) && (
              <button
                onClick={handleToggleDetails}
                className="text-white hover:bg-white hover:bg-opacity-20 px-2 py-1 rounded text-xs"
              >
                {showDetails ? 'Hide' : 'Details'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Details Panel */}
      {showDetails && (
        <div className="bg-white border-b border-gray-200 shadow-sm">
          <div className="max-w-7xl mx-auto px-4 py-3">
            <div className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-900">Sync Status</h4>
              
              {syncError && (
                <div className="flex items-center space-x-2 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm">{syncError}</span>
                </div>
              )}
              
              {pendingUploads > 0 && (
                <div className="text-sm text-gray-600">
                  <p>{pendingUploads} items waiting to sync:</p>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Incident reports, checklists, and risk assessments</li>
                    <li>• Will sync automatically when online</li>
                    <li>• Data is safely stored on your device</li>
                  </ul>
                </div>
              )}
              
              {!isOnline && (
                <div className="text-sm text-gray-600">
                  <p>Offline mode features:</p>
                  <ul className="mt-1 space-y-1 text-xs">
                    <li>• Create and edit reports</li>
                    <li>• Complete checklists</li>
                    <li>• Perform risk assessments</li>
                    <li>• Take photos and add notes</li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
