<!DOCTYPE html>
<html>
<head>
    <title>Icon Converter</title>
</head>
<body>
    <h1>Converting SVG Icons to PNG...</h1>
    <div id="output"></div>
    
    <script>
        const sizes = [72, 96, 128, 144, 152, 192, 384, 512];
        const shortcuts = ['report', 'checklist', 'risk'];
        
        async function convertSVGtoPNG(svgPath, size) {
            try {
                const response = await fetch(svgPath);
                const svgText = await response.text();
                
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = size;
                canvas.height = size;
                
                const img = new Image();
                const svgBlob = new Blob([svgText], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(svgBlob);
                
                return new Promise((resolve) => {
                    img.onload = () => {
                        ctx.drawImage(img, 0, 0, size, size);
                        canvas.toBlob((blob) => {
                            const link = document.createElement('a');
                            link.download = `icon-${size}x${size}.png`;
                            link.href = URL.createObjectURL(blob);
                            link.click();
                            URL.revokeObjectURL(url);
                            resolve();
                        }, 'image/png');
                    };
                    img.src = url;
                });
            } catch (error) {
                console.error(`Error converting ${svgPath}:`, error);
            }
        }
        
        async function convertAll() {
            const output = document.getElementById('output');
            
            // Convert main icons
            for (const size of sizes) {
                await convertSVGtoPNG(`/icons/icon-${size}x${size}.svg`, size);
                output.innerHTML += `<p>Converted icon-${size}x${size}.png</p>`;
            }
            
            // Convert shortcut icons
            for (const shortcut of shortcuts) {
                await convertSVGtoPNG(`/icons/shortcut-${shortcut}.svg`, 96);
                output.innerHTML += `<p>Converted shortcut-${shortcut}.png</p>`;
            }
            
            output.innerHTML += '<p><strong>All conversions complete!</strong></p>';
        }
        
        // Start conversion when page loads
        window.onload = convertAll;
    </script>
</body>
</html>
