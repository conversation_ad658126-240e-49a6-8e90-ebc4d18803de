/**
 * Offline State Redux Slice
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { SyncQueueItem } from '@/types';

interface OfflineState {
  isOnline: boolean;
  syncQueue: SyncQueueItem[];
  isSyncing: boolean;
  lastSyncTime: string | null;
  syncError: string | null;
  pendingUploads: number;
  storageUsage: {
    incidents: number;
    checklists: number;
    riskAssessments: number;
    total: number;
  };
}

const initialState: OfflineState = {
  isOnline: true,
  syncQueue: [],
  isSyncing: false,
  lastSyncTime: null,
  syncError: null,
  pendingUploads: 0,
  storageUsage: {
    incidents: 0,
    checklists: 0,
    riskAssessments: 0,
    total: 0,
  },
};

// Async thunks
export const syncOfflineData = createAsyncThunk(
  'offline/syncData',
  async (_, { rejectWithValue, getState }) => {
    try {
      // TODO: Implement actual sync logic
      const state = getState() as { offline: OfflineState };
      const { syncQueue } = state.offline;

      const results = [];
      for (const item of syncQueue) {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        results.push(item.id);
      }

      return {
        syncedItems: results,
        syncTime: new Date().toISOString(),
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Sync failed');
    }
  }
);

export const loadStorageUsage = createAsyncThunk(
  'offline/loadStorageUsage',
  async (_, { rejectWithValue }) => {
    try {
      // TODO: Implement actual storage usage calculation
      return {
        incidents: 10,
        checklists: 5,
        riskAssessments: 3,
        total: 18,
      };
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to load storage usage');
    }
  }
);

const offlineSlice = createSlice({
  name: 'offline',
  initialState,
  reducers: {
    setOnlineStatus: (state, action: PayloadAction<boolean>) => {
      state.isOnline = action.payload;
    },
    addToSyncQueue: (state, action: PayloadAction<SyncQueueItem>) => {
      state.syncQueue.push(action.payload);
      state.pendingUploads += 1;
    },
    removeFromSyncQueue: (state, action: PayloadAction<string>) => {
      state.syncQueue = state.syncQueue.filter(item => item.id !== action.payload);
      state.pendingUploads = Math.max(0, state.pendingUploads - 1);
    },
    updateSyncItemRetry: (state, action: PayloadAction<string>) => {
      const item = state.syncQueue.find(item => item.id === action.payload);
      if (item) {
        item.retryCount += 1;
        item.lastAttempt = new Date().toISOString();
      }
    },
    clearSyncQueue: (state) => {
      state.syncQueue = [];
      state.pendingUploads = 0;
    },
    setSyncError: (state, action: PayloadAction<string | null>) => {
      state.syncError = action.payload;
    },
    clearSyncError: (state) => {
      state.syncError = null;
    },
  },
  extraReducers: (builder) => {
    // Sync data
    builder
      .addCase(syncOfflineData.pending, (state) => {
        state.isSyncing = true;
        state.syncError = null;
      })
      .addCase(syncOfflineData.fulfilled, (state, action) => {
        state.isSyncing = false;
        state.lastSyncTime = action.payload.syncTime;
        // Remove synced items from queue
        state.syncQueue = state.syncQueue.filter(
          item => !action.payload.syncedItems.includes(item.id)
        );
        state.pendingUploads = state.syncQueue.length;
        state.syncError = null;
      })
      .addCase(syncOfflineData.rejected, (state, action) => {
        state.isSyncing = false;
        state.syncError = action.payload as string;
      });

    // Load storage usage
    builder
      .addCase(loadStorageUsage.fulfilled, (state, action) => {
        state.storageUsage = action.payload;
      });
  },
});

export const {
  setOnlineStatus,
  addToSyncQueue,
  removeFromSyncQueue,
  updateSyncItemRetry,
  clearSyncQueue,
  setSyncError,
  clearSyncError,
} = offlineSlice.actions;

export default offlineSlice.reducer;

// Selectors
export const selectOffline = (state: { offline: OfflineState }) => state.offline;
export const selectIsOnline = (state: { offline: OfflineState }) => state.offline.isOnline;
export const selectSyncQueue = (state: { offline: OfflineState }) => state.offline.syncQueue;
export const selectIsSyncing = (state: { offline: OfflineState }) => state.offline.isSyncing;
export const selectLastSyncTime = (state: { offline: OfflineState }) => state.offline.lastSyncTime;
export const selectSyncError = (state: { offline: OfflineState }) => state.offline.syncError;
export const selectPendingUploads = (state: { offline: OfflineState }) => state.offline.pendingUploads;
export const selectStorageUsage = (state: { offline: OfflineState }) => state.offline.storageUsage;
