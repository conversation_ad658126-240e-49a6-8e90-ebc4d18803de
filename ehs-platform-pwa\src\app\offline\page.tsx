'use client';

/**
 * Offline Page
 * Shown when the user is offline and tries to access unavailable content
 */

import React from 'react';
import { WifiOff, RefreshCw, Home, FileText, CheckSquare } from 'lucide-react';
import Button from '@/components/ui/Button';
import { useAppSelector } from '@/store';

export default function OfflinePage() {
  const { isOnline } = useAppSelector((state) => state.ui);

  const handleRetry = () => {
    if (isOnline) {
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  const offlineFeatures = [
    {
      icon: FileText,
      title: 'Create Reports',
      description: 'Draft incident reports that will sync when online'
    },
    {
      icon: CheckSquare,
      title: 'Complete Checklists',
      description: 'Fill out safety checklists and save them locally'
    },
    {
      icon: WifiOff,
      title: 'View Cached Data',
      description: 'Access previously loaded content and data'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center px-4">
      <div className="max-w-md w-full text-center">
        {/* Offline Icon */}
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center">
            <WifiOff className="h-12 w-12 text-gray-500" />
          </div>
        </div>

        {/* Main Message */}
        <h1 className="text-2xl font-bold text-gray-900 mb-4">
          You're Offline
        </h1>
        
        <p className="text-gray-600 mb-8">
          {isOnline 
            ? "This page isn't available offline, but you can still use many features."
            : "Check your internet connection and try again, or continue working offline."
          }
        </p>

        {/* Action Buttons */}
        <div className="space-y-3 mb-8">
          <Button
            onClick={handleRetry}
            fullWidth
            leftIcon={<RefreshCw className="h-4 w-4" />}
            disabled={!isOnline}
          >
            {isOnline ? 'Retry' : 'Waiting for connection...'}
          </Button>
          
          <Button
            onClick={handleGoHome}
            variant="outline"
            fullWidth
            leftIcon={<Home className="h-4 w-4" />}
          >
            Go to Home
          </Button>
        </div>

        {/* Offline Features */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Available Offline
          </h2>
          
          <div className="space-y-4">
            {offlineFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="h-4 w-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="text-left">
                    <h3 className="text-sm font-medium text-gray-900">
                      {feature.title}
                    </h3>
                    <p className="text-xs text-gray-600 mt-1">
                      {feature.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Connection Status */}
        <div className="mt-6 text-xs text-gray-500">
          Status: {isOnline ? (
            <span className="text-green-600 font-medium">Online</span>
          ) : (
            <span className="text-red-600 font-medium">Offline</span>
          )}
        </div>
      </div>
    </div>
  );
}
