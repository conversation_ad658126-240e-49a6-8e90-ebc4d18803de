@import "tailwindcss";

/* CSS Variables for theming */
:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #1e40af;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #1e40af;
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f172a;
    --foreground: #f8fafc;
    --primary: #3b82f6;
    --primary-foreground: #0f172a;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f8fafc;
    --destructive: #ef4444;
    --destructive-foreground: #f8fafc;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;
  }
}

/* Base styles */
* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: var(--font-inter), system-ui, sans-serif;
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* PWA specific styles */
@media (display-mode: standalone) {
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* iOS specific styles */
@supports (-webkit-touch-callout: none) {
  .ios-safe-area {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Focus styles for accessibility */
.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--foreground));
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Skeleton loading */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Touch feedback */
.touch-feedback:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* Offline indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #f59e0b;
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  z-index: 9999;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced touch feedback */
.touch-feedback {
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.touch-feedback:active {
  transform: scale(0.98);
  transition: transform 0.1s ease-in-out;
}

/* Better focus states for accessibility */
.focus-ring:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Smooth transitions for interactive elements */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced card hover effects */
.card-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Advanced animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
  }
}

/* Animation utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Shimmer effect for loading states */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Glass morphism effects */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Enhanced gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-warning {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.gradient-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ffa500 100%);
}

/* Micro-interaction effects */
.micro-bounce:hover {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Enhanced focus states with better accessibility */
.focus-ring-enhanced:focus-visible {
  outline: 3px solid hsl(var(--ring));
  outline-offset: 2px;
  border-radius: 0.5rem;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
}

/* Staggered animation delays for lists */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }
.stagger-7 { animation-delay: 0.7s; }
.stagger-8 { animation-delay: 0.8s; }

/* Mobile-optimized Quick Access styles */
@media (max-width: 767px) {
  .quick-access-mobile {
    padding: 0.5rem 1rem; /* Reduced from default p-4 */
  }

  .quick-access-grid {
    gap: 0.5rem; /* Reduced gap for mobile */
  }

  .quick-access-card {
    padding: 0.75rem 0.5rem; /* Compact padding */
    min-height: 80px; /* Ensure minimum touch target */
    border-radius: 0.75rem; /* Slightly smaller border radius */
  }

  .quick-access-icon {
    width: 2.5rem; /* 40px - reduced from 56px */
    height: 2.5rem;
    margin-bottom: 0.5rem; /* Reduced margin */
    border-radius: 0.75rem; /* Smaller border radius for mobile */
  }

  .quick-access-icon svg {
    width: 1.25rem; /* 20px - reduced from 28px */
    height: 1.25rem;
  }

  .quick-access-title {
    font-size: 0.6875rem; /* 11px - slightly smaller */
    line-height: 1.2;
    margin-bottom: 0.125rem;
    font-weight: 600; /* Slightly less bold for mobile */
  }

  .quick-access-description {
    font-size: 0.625rem; /* 10px - smaller description */
    line-height: 1.2;
  }

  /* Reduce section spacing on mobile */
  .quick-access-mobile + div {
    margin-top: 0.5rem;
  }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
  .quick-access-mobile {
    padding: 0.5rem 0.75rem;
  }

  .quick-access-grid {
    gap: 0.375rem; /* Even smaller gap */
  }

  .quick-access-card {
    padding: 0.625rem 0.375rem;
    min-height: 72px;
  }

  .quick-access-icon {
    width: 2.25rem; /* 36px */
    height: 2.25rem;
    margin-bottom: 0.375rem;
  }

  .quick-access-title {
    font-size: 0.625rem; /* 10px */
  }
}

/* Ensure touch targets meet accessibility guidelines */
@media (max-width: 767px) {
  .quick-access-card {
    /* Ensure minimum 44px touch target */
    min-height: 80px;
    min-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  /* Optimize for landscape orientation on mobile */
  @media (orientation: landscape) and (max-height: 500px) {
    .quick-access-card {
      min-height: 60px;
      padding: 0.5rem 0.25rem;
    }

    .quick-access-icon {
      width: 2rem;
      height: 2rem;
      margin-bottom: 0.25rem;
    }

    .quick-access-icon svg {
      width: 1rem;
      height: 1rem;
    }

    .quick-access-title {
      font-size: 0.625rem;
      line-height: 1.1;
    }
  }
}

/* Additional mobile performance optimizations */
@media (max-width: 767px) {
  /* Reduce animation complexity on mobile for better performance */
  .quick-access-card .group-hover\:scale-110 {
    transition-duration: 200ms;
  }

  /* Optimize hover states for touch devices */
  @media (hover: none) {
    .quick-access-card:hover {
      transform: none;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .quick-access-description {
      opacity: 0.7; /* Show description on touch devices */
    }
  }

  /* Ensure consistent grid layout on mobile */
  .quick-access-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    justify-items: center;
    align-items: start;
  }

  /* Improve text readability on small screens */
  .quick-access-title {
    text-align: center;
    word-break: break-word;
    hyphens: auto;
  }
}

/* Action card responsive improvements */
@media (max-width: 767px) {
  /* Mobile-optimized action cards */
  .action-card-mobile {
    padding: 1rem; /* Reduced from p-6 */
  }

  .action-card-content {
    flex-direction: column;
    gap: 0.75rem;
  }

  .action-card-metadata {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .action-card-progress {
    width: 3rem; /* Smaller progress bar on mobile */
  }

  .action-card-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .action-card-button {
    align-self: flex-end;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .action-card-mobile {
    padding: 0.75rem;
  }

  .action-card-metadata {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.375rem;
  }

  .action-card-progress {
    width: 2.5rem;
  }
}

/* Improved card header alignment */
.action-card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 0.75rem;
}

.action-card-title-section {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
  gap: 0.5rem;
}

.action-card-status-section {
  display: flex;
  align-items: center;
  margin-left: 1rem;
  flex-shrink: 0;
}

/* Better spacing for mobile cards */
@media (max-width: 767px) {
  .action-card-header {
    margin-bottom: 0.5rem;
  }

  .action-card-status-section {
    margin-left: 0.75rem;
  }

  .action-card-title-section {
    gap: 0.375rem;
  }
}

/* Clean card design matching the provided UI */
.clean-action-card {
  background: white;
  border-radius: 0.5rem;
  border-left: 4px solid;
  padding: 1.25rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: box-shadow 0.2s ease-in-out;
}

.clean-action-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.clean-action-card .card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

.clean-action-card .card-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.clean-action-card .card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clean-action-card .card-due-date {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.clean-action-card .card-action-btn {
  color: #3b82f6;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  transition: color 0.2s ease-in-out;
}

.clean-action-card .card-action-btn:hover {
  color: #2563eb;
}

/* Mobile responsive for clean cards */
@media (max-width: 767px) {
  .clean-action-card {
    padding: 1rem;
  }

  .clean-action-card .card-title {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }

  .clean-action-card .card-subtitle {
    margin-bottom: 0.75rem;
  }
}
