{"name": "EHS Platform - Environment, Health & Safety", "short_name": "EHS Platform", "description": "Professional Environment, Health & Safety management platform for workplace safety, incident reporting, and risk assessment.", "theme_color": "#1e40af", "background_color": "#ffffff", "display": "standalone", "orientation": "portrait-primary", "scope": "/", "start_url": "/", "id": "/", "lang": "en", "dir": "ltr", "categories": ["business", "productivity", "utilities"], "icons": [{"src": "/icons/icon-72x72.svg", "sizes": "72x72", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.svg", "sizes": "96x96", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.svg", "sizes": "128x128", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.svg", "sizes": "144x144", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.svg", "sizes": "152x152", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.svg", "sizes": "192x192", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.svg", "sizes": "384x384", "type": "image/svg+xml", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.svg", "sizes": "512x512", "type": "image/svg+xml", "purpose": "maskable any"}], "shortcuts": [{"name": "Report Incident", "short_name": "Report", "description": "Quickly report a safety incident", "url": "/incident/report", "icons": [{"src": "/icons/shortcut-report.svg", "sizes": "96x96"}]}, {"name": "Safety Checklist", "short_name": "Checklist", "description": "Access safety checklists", "url": "/checklist", "icons": [{"src": "/icons/shortcut-checklist.svg", "sizes": "96x96"}]}, {"name": "Risk Assessment", "short_name": "Risk", "description": "Perform risk assessment", "url": "/risk-assessment", "icons": [{"src": "/icons/shortcut-risk.svg", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/mobile-1.png", "sizes": "540x720", "type": "image/png", "form_factor": "narrow"}, {"src": "/screenshots/mobile-2.png", "sizes": "540x720", "type": "image/png", "form_factor": "narrow"}, {"src": "/screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "capture_links": "existing-client-navigate"}