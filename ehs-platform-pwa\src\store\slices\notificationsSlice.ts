/**
 * Notifications Redux Slice - Placeholder
 */

import { createSlice } from '@reduxjs/toolkit';

interface NotificationsState {
  notifications: any[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
}

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
};

const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // TODO: Implement notification actions
  },
});

export default notificationsSlice.reducer;
