/**
 * Constants for EHS Platform
 */

// App Configuration
export const APP_CONFIG = {
  name: 'EHS Platform',
  shortName: 'EHS',
  description: 'Environment, Health & Safety Management Platform',
  version: '1.0.0',
  author: 'EHS Team',
} as const;

// API Configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_URL || '/api',
  timeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
} as const;

// PWA Configuration
export const PWA_CONFIG = {
  cacheName: 'ehs-platform-v1',
  offlineUrl: '/offline',
  installPromptDelay: 3000,
  maxRetries: 5,
} as const;

// Incident Severity Levels
export const INCIDENT_SEVERITY = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

export const INCIDENT_SEVERITY_LABELS = {
  [INCIDENT_SEVERITY.LOW]: 'Low',
  [INCIDENT_SEVERITY.MEDIUM]: 'Medium',
  [INCIDENT_SEVERITY.HIGH]: 'High',
  [INCIDENT_SEVERITY.CRITICAL]: 'Critical',
} as const;

export const INCIDENT_SEVERITY_COLORS = {
  [INCIDENT_SEVERITY.LOW]: 'bg-green-100 text-green-800 border-green-200',
  [INCIDENT_SEVERITY.MEDIUM]: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  [INCIDENT_SEVERITY.HIGH]: 'bg-orange-100 text-orange-800 border-orange-200',
  [INCIDENT_SEVERITY.CRITICAL]: 'bg-red-100 text-red-800 border-red-200',
} as const;

// Status Types
export const STATUS = {
  DRAFT: 'draft',
  PENDING: 'pending',
  SUBMITTED: 'submitted',
  COMPLETED: 'completed',
  SYNCED: 'synced',
  CANCELLED: 'cancelled',
} as const;

export const STATUS_LABELS = {
  [STATUS.DRAFT]: 'Draft',
  [STATUS.PENDING]: 'Pending',
  [STATUS.SUBMITTED]: 'Submitted',
  [STATUS.COMPLETED]: 'Completed',
  [STATUS.SYNCED]: 'Synced',
  [STATUS.CANCELLED]: 'Cancelled',
} as const;

export const STATUS_COLORS = {
  [STATUS.DRAFT]: 'bg-gray-100 text-gray-800 border-gray-200',
  [STATUS.PENDING]: 'bg-blue-100 text-blue-800 border-blue-200',
  [STATUS.SUBMITTED]: 'bg-purple-100 text-purple-800 border-purple-200',
  [STATUS.COMPLETED]: 'bg-green-100 text-green-800 border-green-200',
  [STATUS.SYNCED]: 'bg-emerald-100 text-emerald-800 border-emerald-200',
  [STATUS.CANCELLED]: 'bg-red-100 text-red-800 border-red-200',
} as const;

// Risk Assessment Levels
export const RISK_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical',
} as const;

export const RISK_LEVEL_LABELS = {
  [RISK_LEVELS.LOW]: 'Low Risk',
  [RISK_LEVELS.MEDIUM]: 'Medium Risk',
  [RISK_LEVELS.HIGH]: 'High Risk',
  [RISK_LEVELS.CRITICAL]: 'Critical Risk',
} as const;

export const RISK_LEVEL_COLORS = {
  [RISK_LEVELS.LOW]: 'bg-green-500',
  [RISK_LEVELS.MEDIUM]: 'bg-yellow-500',
  [RISK_LEVELS.HIGH]: 'bg-orange-500',
  [RISK_LEVELS.CRITICAL]: 'bg-red-500',
} as const;

// Navigation Routes
export const ROUTES = {
  HOME: '/',
  DASHBOARD: '/dashboard',
  INCIDENTS: '/incidents',
  INCIDENT_REPORT: '/incidents/report',
  INCIDENT_DETAIL: '/incidents/[id]',
  CHECKLISTS: '/checklists',
  CHECKLIST_DETAIL: '/checklists/[id]',
  RISK_ASSESSMENTS: '/risk-assessments',
  RISK_ASSESSMENT_NEW: '/risk-assessments/new',
  RISK_ASSESSMENT_DETAIL: '/risk-assessments/[id]',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  OFFLINE: '/offline',
  LOGIN: '/login',
  REGISTER: '/register',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  USER_PREFERENCES: 'ehs_user_preferences',
  OFFLINE_DATA: 'ehs_offline_data',
  SYNC_QUEUE: 'ehs_sync_queue',
  INSTALL_PROMPT: 'ehs_install_prompt_shown',
  ONBOARDING: 'ehs_onboarding_completed',
  THEME: 'ehs_theme',
  LANGUAGE: 'ehs_language',
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000,
} as const;

// Breakpoints (matching Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536,
} as const;

// Touch Target Sizes (in pixels)
export const TOUCH_TARGET = {
  MIN: 44,
  RECOMMENDED: 48,
  LARGE: 56,
} as const;

// File Upload Limits
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_FILES: 5,
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.webp', '.pdf'],
} as const;

// Geolocation Configuration
export const GEOLOCATION_CONFIG = {
  enableHighAccuracy: true,
  timeout: 10000,
  maximumAge: 300000, // 5 minutes
} as const;

// Notification Types
export const NOTIFICATION_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
} as const;

// Theme Configuration
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// Language Configuration
export const LANGUAGES = {
  EN: 'en',
  ES: 'es',
  FR: 'fr',
  DE: 'de',
} as const;

export const LANGUAGE_LABELS = {
  [LANGUAGES.EN]: 'English',
  [LANGUAGES.ES]: 'Español',
  [LANGUAGES.FR]: 'Français',
  [LANGUAGES.DE]: 'Deutsch',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  OFFLINE_ERROR: 'You are offline. Data will be saved locally and synced when online.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  PERMISSION_ERROR: 'Permission denied. Please check your permissions.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
  FILE_TOO_LARGE: 'File is too large. Maximum size is 10MB.',
  INVALID_FILE_TYPE: 'Invalid file type. Please select a valid file.',
  LOCATION_ERROR: 'Unable to get your location. Please enable location services.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  INCIDENT_SAVED: 'Incident report saved successfully.',
  CHECKLIST_COMPLETED: 'Checklist completed successfully.',
  RISK_ASSESSMENT_SAVED: 'Risk assessment saved successfully.',
  DATA_SYNCED: 'Data synchronized successfully.',
  PROFILE_UPDATED: 'Profile updated successfully.',
  SETTINGS_SAVED: 'Settings saved successfully.',
} as const;
