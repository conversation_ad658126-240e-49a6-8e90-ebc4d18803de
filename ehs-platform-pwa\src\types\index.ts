/**
 * Type definitions for EHS Platform
 */

// Base types
export type ID = string;
export type Timestamp = string;

// User types
export interface User {
  id: ID;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  department?: string;
  phone?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type UserRole = 'admin' | 'manager' | 'supervisor' | 'employee' | 'contractor';

// Location types
export interface Location {
  id: ID;
  name: string;
  address: string;
  coordinates?: Coordinates;
  type: LocationType;
  parentId?: ID;
}

export interface Coordinates {
  lat: number;
  lng: number;
  accuracy?: number;
}

export type LocationType = 'site' | 'building' | 'floor' | 'room' | 'area';

// Incident types
export interface Incident {
  id: ID;
  title: string;
  description: string;
  severity: IncidentSeverity;
  type: IncidentType;
  location: string;
  coordinates?: Coordinates;
  reportedBy: ID;
  reportedAt: Timestamp;
  occurredAt: Timestamp;
  status: IncidentStatus;
  photos?: Photo[];
  witnesses?: Witness[];
  immediateActions?: string;
  rootCause?: string;
  correctiveActions?: CorrectiveAction[];
  assignedTo?: ID;
  dueDate?: Timestamp;
  completedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  syncedAt?: Timestamp;
}

export type IncidentSeverity = 'low' | 'medium' | 'high' | 'critical';
export type IncidentType = 'injury' | 'near_miss' | 'property_damage' | 'environmental' | 'security' | 'other';
export type IncidentStatus = 'draft' | 'pending' | 'investigating' | 'resolved' | 'closed';

export interface Witness {
  id: ID;
  name: string;
  email?: string;
  phone?: string;
  statement?: string;
}

export interface CorrectiveAction {
  id: ID;
  description: string;
  assignedTo: ID;
  dueDate: Timestamp;
  status: 'pending' | 'in_progress' | 'completed';
  completedAt?: Timestamp;
  notes?: string;
}

// Checklist types
export interface Checklist {
  id: ID;
  templateId: ID;
  title: string;
  description?: string;
  items: ChecklistItem[];
  completedBy?: ID;
  completedAt?: Timestamp;
  location: string;
  coordinates?: Coordinates;
  status: ChecklistStatus;
  score?: number;
  notes?: string;
  photos?: Photo[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
  syncedAt?: Timestamp;
}

export interface ChecklistTemplate {
  id: ID;
  title: string;
  description?: string;
  category: string;
  items: ChecklistTemplateItem[];
  isActive: boolean;
  version: number;
  createdBy: ID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ChecklistItem {
  id: ID;
  text: string;
  checked: boolean;
  required: boolean;
  notes?: string;
  photos?: Photo[];
  score?: number;
  weight?: number;
}

export interface ChecklistTemplateItem {
  id: ID;
  text: string;
  required: boolean;
  weight?: number;
  helpText?: string;
}

export type ChecklistStatus = 'draft' | 'in_progress' | 'completed' | 'synced';

// Risk Assessment types
export interface RiskAssessment {
  id: ID;
  title: string;
  description: string;
  location: string;
  coordinates?: Coordinates;
  assessedBy: ID;
  assessedAt: Timestamp;
  validUntil?: Timestamp;
  hazards: Hazard[];
  overallRisk: RiskLevel;
  status: RiskAssessmentStatus;
  approvedBy?: ID;
  approvedAt?: Timestamp;
  reviewDate?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  syncedAt?: Timestamp;
}

export interface Hazard {
  id: ID;
  description: string;
  category: HazardCategory;
  likelihood: number; // 1-5 scale
  severity: number; // 1-5 scale
  riskLevel: RiskLevel;
  existingControls: string[];
  additionalControls: string[];
  residualRisk: RiskLevel;
  photos?: Photo[];
}

export type HazardCategory = 'physical' | 'chemical' | 'biological' | 'ergonomic' | 'psychosocial' | 'environmental';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type RiskAssessmentStatus = 'draft' | 'pending_approval' | 'approved' | 'expired' | 'archived';

// File and Photo types
export interface Photo {
  id: ID;
  url: string;
  filename: string;
  size: number;
  mimeType: string;
  caption?: string;
  uploadedAt: Timestamp;
  uploadedBy: ID;
}

export interface FileUpload {
  file: File;
  preview?: string;
  progress?: number;
  error?: string;
}

// Notification types
export interface Notification {
  id: ID;
  title: string;
  message: string;
  type: NotificationType;
  userId: ID;
  read: boolean;
  actionUrl?: string;
  actionText?: string;
  createdAt: Timestamp;
  readAt?: Timestamp;
}

export type NotificationType = 'incident' | 'checklist' | 'risk_assessment' | 'system' | 'reminder';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: Pagination;
}

export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: FormFieldType;
  required?: boolean;
  placeholder?: string;
  options?: FormOption[];
  validation?: ValidationRule[];
  helpText?: string;
}

export type FormFieldType = 'text' | 'email' | 'password' | 'number' | 'select' | 'multiselect' | 'textarea' | 'checkbox' | 'radio' | 'date' | 'time' | 'datetime' | 'file' | 'location';

export interface FormOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface ValidationRule {
  type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
}

// PWA types
export interface PWAInstallPrompt {
  canInstall: boolean;
  isInstalled: boolean;
  isStandalone: boolean;
  platform: string;
}

export interface OfflineData {
  incidents: Incident[];
  checklists: Checklist[];
  riskAssessments: RiskAssessment[];
  lastSync: Timestamp;
}

export interface SyncQueueItem {
  id: ID;
  type: 'incident' | 'checklist' | 'riskAssessment';
  action: 'create' | 'update' | 'delete';
  data: any;
  retryCount: number;
  createdAt: Timestamp;
  lastAttempt?: Timestamp;
}

// Settings types
export interface UserSettings {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: NotificationSettings;
  privacy: PrivacySettings;
  accessibility: AccessibilitySettings;
}

export interface NotificationSettings {
  push: boolean;
  email: boolean;
  incidents: boolean;
  checklists: boolean;
  riskAssessments: boolean;
  reminders: boolean;
}

export interface PrivacySettings {
  shareLocation: boolean;
  shareUsageData: boolean;
  allowAnalytics: boolean;
}

export interface AccessibilitySettings {
  fontSize: 'small' | 'medium' | 'large';
  highContrast: boolean;
  reduceMotion: boolean;
  screenReader: boolean;
}

// Component Props types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showFirstLast?: boolean;
  showPrevNext?: boolean;
  maxVisiblePages?: number;
}

// Search and Filter types
export interface SearchFilters {
  query?: string;
  status?: string[];
  severity?: string[];
  type?: string[];
  dateFrom?: string;
  dateTo?: string;
  location?: string;
  assignedTo?: string;
}

export interface SortOption {
  field: string;
  direction: 'asc' | 'desc';
  label: string;
}
